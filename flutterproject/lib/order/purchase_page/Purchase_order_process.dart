import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'purchase_order_completed.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '订单追踪',
      theme: ThemeData(
        primarySwatch: Colors.purple,
        scaffoldBackgroundColor: const Color.fromARGB(255, 255, 255, 255),
      ),
      home: const OrderTrackingScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// 订单信息数据模型
class OrderInfo {
  final String orderId;
  final String itemInfo;
  final String merchant;
  final double itemValue;
  final double serviceFee;
  final double deposit;
  final double totalAmount;
  final String deliveryAddress;
  final String deliveryTime;
  final String volume;
  final String weight;

  OrderInfo({
    required this.orderId,
    required this.itemInfo,
    required this.merchant,
    required this.itemValue,
    required this.serviceFee,
    required this.deposit,
    required this.totalAmount,
    required this.deliveryAddress,
    required this.deliveryTime,
    required this.volume,
    required this.weight,
  });
}

class OrderTrackingScreen extends StatefulWidget {
  final OrderInfo? orderInfo;

  const OrderTrackingScreen({Key? key, this.orderInfo}) : super(key: key);

  @override
  State<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

class _OrderTrackingScreenState extends State<OrderTrackingScreen> {
  Timer? _timer;
  int _remainingSeconds = 2 * 3600 + 28 * 60 + 45; // 2小时28分钟45秒

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          timer.cancel();
        }
      });
    });
  }

  String get _formattedTime {
    int hours = _remainingSeconds ~/ 3600;
    int minutes = (_remainingSeconds % 3600) ~/ 60;
    int seconds = _remainingSeconds % 60;
    return '${hours.toString().padLeft(2, '0')} 小时 ${minutes.toString().padLeft(2, '0')} 分钟 ${seconds.toString().padLeft(2, '0')} 秒';
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ));

    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (!didPop) {
          final shouldExit = await _showExitConfirmDialog(context);
          if (shouldExit && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        backgroundColor: const Color.fromARGB(255, 255, 255, 255),
        body: SafeArea(
        child: Column(
          children: [
            // 顶部导航栏
            _buildAppBar(),
            // 倒计时进度条
            _buildCountdownBar(),
            // 内容区域
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // 地图区域
                    _buildMapSection(),
                    const SizedBox(height: 12),
                    // 司机信息卡片
                    _buildDriverInfoCard(),
                    const SizedBox(height: 12),
                    // 商品信息
                    // _buildProductInfo(),
                    _buildBottomActions(),
                    const SizedBox(height: 80), // 为底部按钮留空间
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
        // 底部导航栏
        bottomNavigationBar: _buildBottomNavBar(),
      ),
    );
  }

  // 显示退出确认对话框
  Future<bool> _showExitConfirmDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('订单正在进行中，确定要退出吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    ) ?? false;
  }

  // 处理完成订单
  Future<void> _handleCompleteOrder() async {
    try {
      // 显示确认对话框
      final confirmed = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('确认完成订单'),
          content: const Text('确认要完成此订单吗？完成后将无法撤销。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('确认完成'),
            ),
          ],
        ),
      );

      if (confirmed == true && mounted) {
        // 显示处理中的提示
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('正在完成订单...'),
              ],
            ),
          ),
        );

        // 模拟处理延迟
        await Future.delayed(const Duration(seconds: 2));

        if (mounted) {
          // 关闭处理中对话框
          Navigator.of(context).pop();

          // 创建订单完成信息
          final orderCompleteInfo = _createOrderCompleteInfo();

          // 跳转到订单完成页面，清除所有订单相关的中间页面
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => OrderCompletePage(orderInfo: orderCompleteInfo),
            ),
            (route) => route.isFirst, // 保留根页面
          );
        }
      }
    } catch (e) {
      // 处理异常情况
      if (mounted) {
        // 如果有处理中对话框，先关闭它
        Navigator.of(context).popUntil((route) => route is! DialogRoute);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('完成订单失败，请重试'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // 创建订单完成信息
  OrderCompleteInfo _createOrderCompleteInfo() {
    final orderInfo = widget.orderInfo;

    return OrderCompleteInfo(
      orderId: orderInfo?.orderId ?? 'ORD${DateTime.now().millisecondsSinceEpoch}',
      itemInfo: orderInfo?.itemInfo ?? '小龙虾',
      merchant: orderInfo?.merchant ?? '店铺A',
      itemValue: orderInfo?.itemValue ?? 100.0,
      serviceFee: orderInfo?.serviceFee ?? 6.0,
      deposit: orderInfo?.deposit ?? 10.0,
      totalAmount: orderInfo?.totalAmount ?? 116.0,
      deliveryAddress: orderInfo?.deliveryAddress ?? '某某街道',
      pickupAddress: orderInfo?.merchant ?? '店铺A',
      deliveryTime: orderInfo?.deliveryTime ?? '1小时0分钟',
      volume: orderInfo?.volume ?? '0',
      weight: orderInfo?.weight ?? '1',
      staffName: 'Marvis Ighedosa',
      staffEmail: '<EMAIL>',
      completedTime: DateTime.now(),
      paymentStatus: '已支付',
    );
  }

  Widget _buildAppBar() {
    return Container(
      height: 56,
      color: Colors.white,
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 20, color: Color(0xFF333333)),
            onPressed: () async {
              final shouldExit = await _showExitConfirmDialog(context);
              if (shouldExit && mounted) {
                Navigator.of(context).pop();
              }
            },
          ),
          const Expanded(
            child: Center(
              child: Text(
                '订单进行中',
                style: TextStyle(
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF333333),
                ),
              ),
            ),
          ),
          const SizedBox(width: 48), // 平衡左侧返回按钮
        ],
      ),
    );
  }

  Widget _buildCountdownBar() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                '剩余时间',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                _formattedTime,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF333333),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: 0.7, // 进度值
              minHeight: 6,
              backgroundColor: const Color.fromARGB(255, 255, 255, 255),
              valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF9575CD)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapSection() {
    return Container(
      height: 200,
      color: Colors.white,
      child: Stack(
        children: [
          // 模拟地图背景
          Container(
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 255, 255, 255),
              image: DecorationImage(
                image: NetworkImage('https://via.placeholder.com/400x200/E8F5E9/4CAF50?text=Map'),
                fit: BoxFit.cover,
              ),
            ),
          ),
          // 商店图标
          const Positioned(
            left: 50,
            top: 80,
            child: CircleAvatar(
              radius: 20,
              backgroundColor: Color(0xFF9575CD),
              child: Icon(Icons.shopping_bag, color: Colors.white, size: 20),
            ),
          ),
          // 目的地图标
          const Positioned(
            right: 80,
            bottom: 60,
            child: Icon(
              Icons.location_on,
              size: 32,
              color: Color(0xFF2196F3),
            ),
          ),
          // 找我按钮
          Positioned(
            right: 16,
            bottom: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF9575CD),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(
                '找我',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDriverInfoCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 司机信息头部
          Row(
            children: [
              const Text(
                '众觅',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF333333),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 255, 255, 255),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'BOSS保证金•',
                  style: TextStyle(
                    fontSize: 11,
                    color: Color(0xFFFF9800),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // 时间和价格信息
          Row(
            children: [
              _buildTimePoint('10:00am', true),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('物品价值', style: TextStyle(fontSize: 12, color: Color(0xFF999999))),
                        const Text('staff保证金', style: TextStyle(fontSize: 12, color: Color(0xFF999999))),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('100MOP', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
                        const Text('10MOP', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('服务费', style: TextStyle(fontSize: 12, color: Color(0xFF999999))),
                        const Text('积分', style: TextStyle(fontSize: 12, color: Color(0xFF999999))),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('6MOP', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
                        const Text('50MOP', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildTimePoint('10:00am', false),
          const SizedBox(height: 12),
          // 物品信息
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '物品信息                                     小龙虾',
                  style: TextStyle(fontSize: 13, color: Color(0xFF666666)),
                ),
                const SizedBox(height: 8),
                Row(
                  children: const [
                    Text('限时: 1小时 0 分钟', style: TextStyle(fontSize: 12, color: Color(0xFF999999))),
                    SizedBox(width: 16),
                    Text('体积: 0 cm³', style: TextStyle(fontSize: 12, color: Color(0xFF999999))),
                    SizedBox(width: 16),
                    Text('物品重量: 1 KG', style: TextStyle(fontSize: 12, color: Color(0xFF999999))),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // 地址信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('配送地址:', style: TextStyle(fontSize: 13, color: Color(0xFF999999))),
              TextButton(
                onPressed: () {},
                child: const Text('某某街道', style: TextStyle(fontSize: 13, color: Color(0xFF333333))),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('送达地址:', style: TextStyle(fontSize: 13, color: Color(0xFF999999))),
              TextButton(
                onPressed: () {},
                child: const Text('某某街道', style: TextStyle(fontSize: 13, color: Color(0xFF333333))),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('送达时间:', style: TextStyle(fontSize: 13, color: Color(0xFF999999))),
              TextButton(
                onPressed: () {},
                child: const Text('立即送货', style: TextStyle(fontSize: 13, color: Color(0xFF333333))),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // 取件码
          Row(
            children: [
              const Text('取件码: ', style: TextStyle(fontSize: 13, color: Color(0xFF999999))),
              const Text('15a6765', style: TextStyle(fontSize: 13, color: Color(0xFF9575CD), fontWeight: FontWeight.w500)),
              const Spacer(),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF9575CD),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                ),
                child: const Text('接受改价', style: TextStyle(fontSize: 13)),
              ),
              const SizedBox(width: 8),
              OutlinedButton(
                onPressed: () {},
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: Color(0xFF9575CD)),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                ),
                child: const Text('订单异常？', style: TextStyle(fontSize: 13, color: Color(0xFF9575CD))),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimePoint(String time, bool isStart) {
    return Column(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: isStart ? const Color(0xFF9575CD) : const Color(0xFF2196F3),
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          time,
          style: const TextStyle(fontSize: 11, color: Color(0xFF999999)),
        ),
      ],
    );
  }

  // Widget _buildProductInfo() {
  //   return Container(
  //     margin: const EdgeInsets.symmetric(horizontal: 16),
  //     padding: const EdgeInsets.all(16),
  //     decoration: BoxDecoration(
  //       color: Colors.white,
  //       borderRadius: BorderRadius.circular(12),
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         const Text(
  //           '商品信息',
  //           style: TextStyle(
  //             fontSize: 16,
  //             fontWeight: FontWeight.w600,
  //             color: Color(0xFF333333),
  //           ),
  //         ),
  //         const SizedBox(height: 12),
  //         _buildProductItem('牛油火锅底料', '白特', '100MOP', 1),
  //         const Divider(height: 24),
  //         _buildProductItem('牛油火锅底料', '白特', '100MOP', 1),
  //         const SizedBox(height: 12),
  //         InkWell(
  //           onTap: () {},
  //           child: Row(
  //             mainAxisAlignment: MainAxisAlignment.center,
  //             children: const [
  //               Text(
  //                 '使用优惠券',
  //                 style: TextStyle(
  //                   fontSize: 14,
  //                   color: Color(0xFF9575CD),
  //                 ),
  //               ),
  //               SizedBox(width: 4),
  //               Text(
  //                 '通用券10 >',
  //                 style: TextStyle(
  //                   fontSize: 14,
  //                   color: Color(0xFF999999),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildProductItem(String name, String brand, String price, int quantity) {
    return Row(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.image, color: Color(0xFF999999)),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF333333),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                brand,
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF999999),
                ),
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              price,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF333333),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'X$quantity',
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF999999),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          OutlinedButton(
            onPressed: () {},
            style: OutlinedButton.styleFrom(
              minimumSize: const Size(double.infinity, 44),
              side: const BorderSide(color: Color(0xFF9575CD)),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
            ),
            child: const Text(
              '拨打Staff电话',
              style: TextStyle(fontSize: 15, color: Color(0xFF9575CD)),
            ),
          ),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: _handleCompleteOrder,
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 44),
              backgroundColor: const Color(0xFF9575CD),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
            ),
            child: const Text(
              '完成订单',
              style: TextStyle(fontSize: 15, color: Colors.white),
            ),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: () {},
            child: const Text(
              '取消订单',
              style: TextStyle(fontSize: 14, color: Color(0xFF999999)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavBar() {
    return Container(
      height: 56,
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Color(0xFFE5E5E5), width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(Icons.receipt_long, 'BOSS订单', true),
          _buildNavItem(Icons.list_alt, 'Staff订单', false),
          _buildNavItem(Icons.store, 'Staff买家场', false),
          _buildNavItem(Icons.local_shipping, '完成订单', false),
        ],
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, bool isActive) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 20,
          color: isActive ? const Color(0xFF9575CD) : const Color(0xFF999999),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: isActive ? const Color(0xFF9575CD) : const Color(0xFF999999),
          ),
        ),
      ],
    );
  }
}